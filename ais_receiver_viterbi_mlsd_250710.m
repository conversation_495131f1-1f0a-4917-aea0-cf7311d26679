clear;

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA        = 1;                % 0: Ch1, 1: Ch2 Raw data
ENABLE_NOTCH_FLT        = 0;                % 0: Disable, 1: Enable Notch Filter
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: New GMSK Filter
ENABLE_ADAPT_DC_OFFSET  = 1;                % 0: Disable, 1: Adaptive DC Offset
ADAPTIVE_DC_METHOD      = 4;                % 1: Enhanced, 2: <PERSON><PERSON>, 3: HP<PERSON>, 4: Original
ENABLE_ADC_LIMIT        = 2;                % 0: Disable, 1: Min/Max Enable, 2: Max only Enable

% Viterbi MLSD 설정
ENABLE_VITERBI_MLSD     = 1;                % 0: Disable, 1: Enable Viterbi MLSD
VITERBI_WINDOW_SIZE     = 32;               % Viterbi 윈도우 크기 (심벌 단위)
VITERBI_TRACEBACK_DEPTH = 16;              % Traceback 깊이
ENABLE_CHANNEL_ESTIMATION = 1;             % 0: Disable, 1: Enable Channel Estimation
CHANNEL_UPDATE_RATE     = 0.1;             % 채널 추정 업데이트 비율

% 기존 방법 최적화 파라미터 (ADAPTIVE_DC_METHOD = 4일 때 사용)
DC_BASE_ALPHA           = 0.850;            % 기본 알파값 (0.7~0.95 권장)
DC_ALPHA_MIN            = 0.750;            % 최소 알파값
DC_ALPHA_MAX            = 0.950;            % 최대 알파값
DC_STABILITY_THRESHOLD  = 0.001;            % DC 안정성 임계값
DC_VARIANCE_WINDOW      = 20;               % DC 변화량 윈도우 크기
%-------------------------------------------------------------------------
ENABLE_DEBUG            = 1;
ENABLE_FREQ_ANALYSIS    = 1;                % 0: Disable, 1: Enable Frequency Analysis Plots
if (ENABLE_DEBUG == 1)
    ENABLE_PLOT1        = 1;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 1;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 1;                % 0: Disable, 1: Received Data packet with CRC
else
    ENABLE_PLOT1        = 0;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 0;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 0;                % 0: Disable, 1: Received Data packet with CRC
end

ENABLE_DEBUG_ERROR      = 1;
if (ENABLE_DEBUG_ERROR == 1)
    ENABLE_PLOT96       = 1;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 1;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 1;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 1;                % 0: Disable, 1: CRC Error
else
    ENABLE_PLOT96       = 0;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 0;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 0;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 0;                % 0: Disable, 1: CRC Error
end

%-------------------------------------------------------------------------
BIT_RATE                = 9600;             % Bit rate
OSR                     = 5;                % Over sampling rate
BT                      = 0.4;              % Transmit BT product
RX_BT                   = 0.5;              % Receive BT product
LEN_PSF                 = 8 * OSR;          % Pulse shaping filter length
H_NORM                  = 3;                % 1: normalized by h_max, 2: normalized by norm(h), 3: no normalized

ADC_RES                 = 12;               % 12bit resolution
ADC_MAX_VALUE           = 4095;
ADC_MAX_ERROR_CNT       = 30;

%-------------------------------------------------------------------------
MAX_SYNC_CORRVAL        = .755;
MAX_SYNC_COUNT          = 25;
DC_AVG_OFFSET           = (OSR*5);
DC_AVG_COUNT            = 55;
DC_GAP                  = 0.000;
START_DETECT_OFFSET     = (OSR*8);
SYNC_DETECT_OFFSET      = (OSR*6)+2;
ADC_SUB_DC_OFFSET       = 1450;

%-------------------------------------------------------------------------
 % Legacy modem defines
 NOTCH_FLT_A            = [+1.999986841577810, -0.999986910116283];
 NOTCH_FLT_B            = [+0.999993455058141, -1.999986841577810, +0.999993455058141];
 
 DC_MIN_LEVEL           = (   0 / 3300);    % 0.05V
 DC_MID_LEVEL           = (1000 / 3300);    % 1.00V
 DC_MAX_LEVEL           = (1850 / 3300);    % 1.85V

 RX_PLL_FULL            = 2400;
 RX_PLL_HALF            = (RX_PLL_FULL / 2);
 RX_PLL_INCR            = (RX_PLL_FULL / OSR);
 RX_PLL_STEP            = (RX_PLL_INCR / 3);

 RX_GMSK_BT_0_4_FIR_N   = 17;
 RX_GMSK_BT_0_5_FIR_N   = 13;
 RX_GMSK_TO_INT_FACTOR  = 16;

 RX_GMSK_MAX_DATA_VALUE = (BIT_RATE*OSR*RX_GMSK_TO_INT_FACTOR);

 RX_MDM_STATUS_PREAMBLE = 0;
 RX_MDM_STATUS_START    = 1;
 RX_MDM_STATUS_PRELOAD  = 2;
 RX_MDM_STATUS_DATA     = 3;

 RX_DOT_MAX_CNT_SIZE    = 7;
 RX_DOT_MAX_CNT_MASK    = 0x7f;
 RX_DOT_START_P_MASK    = 0x05;
 RX_DOT_DETCT_P_MASK    = 0x55;
 RX_DOT_MAX_CNT_LAST    = RX_DOT_MAX_CNT_SIZE;

 RX_PRE_MAX_CNT_SIZE    = 12;
 RX_PRE_MAX_BUF_SIZE    = (RX_PRE_MAX_CNT_SIZE * OSR);

 G_vNotchDataX = zeros(1, 3);

 G_vReverDataTableX     = [ ...
  %   0    1    2    3   4    5    6    7   8    9    a    b   c    d    e    f 
      0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1, ... % 00--0f
      8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1, ... % 10--1f
      4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1, ... % 20--2f
     12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1, ... % 30--3f
      2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1, ... % 40--4f
     10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1, ... % 50--5f
      6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1, ... % 60--6f
     14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1, ... % 70--7f
      1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1, ... % 80--8f
      9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1, ... % 90--9f
      5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1, ... % a0--af
     13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1, ... % b0--bf
      3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1, ... % c0--cf
     11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1, ... % d0--df
      7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1, ... % e0--ef
     15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1];    % f0--ff

 G_vMaxBitSize          = [ ...
     1064,  168,  168,  168,  168,  424, 1008,  168, 1008,  168,   72,  168, 1008,  168, 1008,  160, ...
      144,  816,  168,  312,  160,  360,  168,  160,  168,  168, 1064,   96, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064];

%-------------------------------------------------------------------------
dot_pattern             = repmat([1, 1, 0, 0], 1, 6);       % Dot pattern
preamble                = dot_pattern';                     % Preamble (dot pattern only)
preamble_os             = repelem(preamble, OSR);           % Over sampled preamble
LEN_DOT_PATTERN         = length(dot_pattern);              % Length of dot pattern
LEN_PREAMBLE            = length(preamble);                 % Length of preamble
LEN_PREAMBLE_OS         = LEN_PREAMBLE*OSR;                 % Length of over sampled preamble
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% functions
%-------------------------------------------------------------------------
% Parameters:
% BT: 대역폭-시간 곱
% OSR: 오버샘플링 비율
% LENGTH: 임펄스 응답의 길이
% NORM: Normalize method
function [h, t] = gmsk_impulse_response(BT, OSR, LENGTH, NORM)
% h: impulse response
% t: time index
    t = ((-LENGTH / 2):(LENGTH / 2)) / OSR;
    h = 0.5 * (erf(pi * BT * sqrt(2 / log(2)) * (t + 0.5)) ...
             - erf(pi * BT * sqrt(2 / log(2)) * (t - 0.5)));

    if (NORM == 1)
        h = h / max(h);
    elseif (NORM == 2)
        h = h / norm(h);
    end
end

% Notch Filter 적용
function y = apply_notch_filter(x, notch_state, NOTCH_FLT_A, NOTCH_FLT_B)
    rY = notch_state(1) + NOTCH_FLT_B(1) * x;
    notch_state(1) = (NOTCH_FLT_B(2) * x) + (NOTCH_FLT_A(1) * rY) + notch_state(2);
    notch_state(2) = (NOTCH_FLT_B(3) * x) + (NOTCH_FLT_A(2) * rY) + notch_state(3);
    y = rY;
end

%------------------------------------------------------------------------
% GMSK Filter 적용
function y = apply_gmsk_filter(x_buff, impulse_response)
    conv_data = conv(x_buff, impulse_response);
    y = conv_data(length(impulse_response));
end

%------------------------------------------------------------------------
% 개선된 Adaptive DC Offset 보정 (다중 시간 상수 + 히스테리시스)
function [G_wRxReferValue, dc_state] = update_dc_offset_enhanced(G_wRxReferValue, sample, alpha_dc, signal_quality, dc_state)
    persistent dc_history dc_variance_est

    if isempty(dc_history)
        dc_history = zeros(1, 10);
        dc_variance_est = 0.01;
    end

    % 히스테리시스 임계값 (신호 품질에 따라 조정)
    if signal_quality > 2.5
        hysteresis_threshold = 0.005;  % 신호가 좋으면 민감하게
    elseif signal_quality > 1.5
        hysteresis_threshold = 0.010;  % 기본값
    else
        hysteresis_threshold = 0.020;  % 신호가 나쁘면 둔감하게
    end

    % 현재 샘플과 DC 레퍼런스의 차이
    dc_error = abs(sample - G_wRxReferValue);

    % 히스테리시스 체크: 임계값을 넘어야만 업데이트
    if dc_error > hysteresis_threshold
        % 다중 시간 상수 적응형 필터
        % 빠른 적응 (단기)
        alpha_fast = alpha_dc;
        dc_fast = alpha_fast * G_wRxReferValue + (1-alpha_fast) * sample;

        % 느린 적응 (장기 안정성)
        alpha_slow = alpha_dc * 0.3;  % 더 느린 적응
        dc_slow = alpha_slow * G_wRxReferValue + (1-alpha_slow) * sample;

        % 신호 변화율에 따른 가중 조합
        dc_history = [dc_history(2:end), sample];
        dc_variance = var(dc_history);
        dc_variance_est = 0.9 * dc_variance_est + 0.1 * dc_variance;

        % 변화율이 클 때는 빠른 적응, 안정할 때는 느린 적응
        if dc_variance_est > 0.02
            weight_fast = 0.8;  % 변화가 클 때
        else
            weight_fast = 0.3;  % 안정할 때
        end

        G_wRxReferValue = weight_fast * dc_fast + (1-weight_fast) * dc_slow;
        dc_state.updated = true;
        dc_state.error = dc_error;
    else
        dc_state.updated = false;
        dc_state.error = dc_error;
    end
end

%------------------------------------------------------------------------
% 예측 기반 DC Offset 보정 (Kalman 필터 기반)
function [G_wRxReferValue, kalman_state] = update_dc_offset_kalman(G_wRxReferValue, sample, kalman_state)
    persistent P Q R x_est

    if isempty(P)
        % Kalman 필터 초기화
        P = 1.0;        % 추정 오차 공분산
        Q = 0.001;      % 프로세스 노이즈
        R = 0.01;       % 측정 노이즈
        x_est = G_wRxReferValue;
    end

    % 예측 단계
    x_pred = x_est;  % DC는 천천히 변한다고 가정
    P_pred = P + Q;

    % 업데이트 단계
    K = P_pred / (P_pred + R);  % Kalman 게인
    x_est = x_pred + K * (sample - x_pred);
    P = (1 - K) * P_pred;

    G_wRxReferValue = x_est;
    kalman_state.gain = K;
    kalman_state.variance = P;
end

%------------------------------------------------------------------------
% 주파수 도메인 DC 제거 (이동평균 기반 고역통과)
function [G_wRxReferValue, hpf_state] = update_dc_offset_hpf(sample, hpf_state)
    persistent dc_buffer buffer_size

    if isempty(dc_buffer)
        buffer_size = 50;  % 이동평균 윈도우 크기
        dc_buffer = zeros(1, buffer_size);
    end

    % 이동평균 업데이트
    dc_buffer = [dc_buffer(2:end), sample];
    dc_estimate = mean(dc_buffer);

    G_wRxReferValue = dc_estimate;
    hpf_state.buffer_full = true;
    hpf_state.variance = var(dc_buffer);
end

%------------------------------------------------------------------------
% 주파수 스펙트럼 분석 함수
function analyze_frequency_spectrum(data, fs, title_str, fig_num)
    % FFT를 사용한 주파수 스펙트럼 분석

    % 데이터 길이 확인
    N = length(data);
    if N < 1024
        fprintf('Warning: Data length too short for meaningful frequency analysis\n');
        return;
    end

    % 윈도우 적용 (Hanning window)
    windowed_data = data .* hanning(N)';

    % FFT 계산
    Y = fft(windowed_data);
    P2 = abs(Y/N);
    P1 = P2(1:N/2+1);
    P1(2:end-1) = 2*P1(2:end-1);

    % 주파수 축 생성
    f = fs*(0:(N/2))/N;

    % 플롯
    figure(fig_num);
    subplot(2,1,1);
    plot(f/1000, 20*log10(P1)); % dB 스케일
    title(sprintf('%s - Frequency Spectrum', title_str));
    xlabel('Frequency (kHz)');
    ylabel('Magnitude (dB)');
    grid on;
    xlim([0 fs/2000]); % Nyquist frequency까지

    % 시간 도메인 신호도 표시
    subplot(2,1,2);
    t = (0:N-1)/fs;
    plot(t*1000, data);
    title(sprintf('%s - Time Domain Signal', title_str));
    xlabel('Time (ms)');
    ylabel('Amplitude');
    grid on;
    xlim([0 min(100, t(end)*1000)]); % 최대 100ms까지 표시
end

%------------------------------------------------------------------------
% 스펙트로그램 분석 함수
function analyze_spectrogram(data, fs, title_str, fig_num)
    % 스펙트로그램을 사용한 시간-주파수 분석

    N = length(data);
    if N < 2048
        fprintf('Warning: Data length too short for spectrogram analysis\n');
        return;
    end

    % 스펙트로그램 파라미터
    window_length = min(512, floor(N/8));
    overlap = floor(window_length * 0.75);
    nfft = max(512, 2^nextpow2(window_length));

    figure(fig_num);
    spectrogram(data, window_length, overlap, nfft, fs, 'yaxis');
    title(sprintf('%s - Spectrogram', title_str));
    colorbar;
    ylim([0 fs/2000]); % kHz 단위로 표시
end

%------------------------------------------------------------------------
% 신호 품질 분석 함수
function signal_stats = analyze_signal_quality(data, fs)
    % 신호 품질 지표 계산

    % 기본 통계
    signal_stats.mean = mean(data);
    signal_stats.std = std(data);
    signal_stats.rms = rms(data);
    signal_stats.peak_to_peak = max(data) - min(data);

    % SNR 추정 (간단한 방법)
    signal_power = var(data);
    % 고주파 성분을 노이즈로 가정
    [b, a] = butter(4, 0.8); % 80% Nyquist 이상을 노이즈로 간주
    noise_estimate = data - filtfilt(b, a, data);
    noise_power = var(noise_estimate);
    signal_stats.snr_estimate = 10*log10(signal_power/noise_power);

    % 주파수 도메인 분석
    N = length(data);
    Y = fft(data);
    P = abs(Y).^2/N;

    % 피크 주파수 찾기
    [~, peak_idx] = max(P(1:floor(N/2)));
    signal_stats.peak_frequency = (peak_idx-1) * fs / N;

    % 대역폭 계산 (-3dB 기준)
    peak_power = P(peak_idx);
    half_power = peak_power / 2;

    % 피크 주변에서 -3dB 지점 찾기
    left_idx = peak_idx;
    right_idx = peak_idx;

    while left_idx > 1 && P(left_idx) > half_power
        left_idx = left_idx - 1;
    end

    while right_idx < floor(N/2) && P(right_idx) > half_power
        right_idx = right_idx + 1;
    end

    signal_stats.bandwidth_3db = (right_idx - left_idx) * fs / N;

    % THD 계산 (Total Harmonic Distortion)
    fundamental_power = P(peak_idx);
    harmonic_power = 0;
    for h = 2:5 % 2차~5차 고조파
        harmonic_idx = h * peak_idx;
        if harmonic_idx <= floor(N/2)
            harmonic_power = harmonic_power + P(harmonic_idx);
        end
    end
    signal_stats.thd = sqrt(harmonic_power / fundamental_power) * 100; % 퍼센트
end

%------------------------------------------------------------------------
% DC 성능 분석 및 리포트
function analyze_dc_performance(dc_error_history, dc_update_count, method_name)
    if dc_update_count > 0
        valid_errors = dc_error_history(1:min(dc_update_count, 1000));
        avg_error = mean(valid_errors);
        std_error = std(valid_errors);
        max_error = max(valid_errors);

        fprintf('\n=== DC Offset Performance Analysis (%s) ===\n', method_name);
        fprintf('Total Updates: %d\n', dc_update_count);
        fprintf('Average Error: %.6f\n', avg_error);
        fprintf('Std Deviation: %.6f\n', std_error);
        fprintf('Max Error: %.6f\n', max_error);
        fprintf('Error Stability: %.2f%%\n', (1 - std_error/avg_error) * 100);

        % 에러 히스토그램 표시 (옵션)
        if dc_update_count > 100
            figure(200);
            histogram(valid_errors, 50);
            title(sprintf('DC Error Distribution (%s)', method_name));
            xlabel('DC Error');
            ylabel('Frequency');
            grid on;
        end
    end
end

%------------------------------------------------------------------------
% NRZI 해석
function bit = nrzi_decode(sample, refer)
    if sample > refer
        bit = 1;
    else
        bit = 0;
    end
end

%------------------------------------------------------------------------
% 채널 임펄스 응답 추정
function [h0, h1, bias] = estimate_channel_response(received_preamble, known_preamble, OSR)
    % 최소자승법을 사용한 채널 추정
    % received_preamble: 수신된 프리앰블 신호
    % known_preamble: 알려진 프리앰블 패턴
    % OSR: 오버샘플링 비율

    if length(received_preamble) < length(known_preamble)
        h0 = 1.0;
        h1 = 0.0;
        bias = 0.0;
        return;
    end

    % 채널 모델: y[n] = h0*x[n] + h1*x[n-OSR] + bias + noise
    N = length(known_preamble);
    A = zeros(N, 3);

    for i = 1:N
        A(i, 1) = known_preamble(i);  % h0 계수
        if i > OSR
            A(i, 2) = known_preamble(i-OSR);  % h1 계수 (ISI)
        end
        A(i, 3) = 1;  % bias 계수
    end

    % 최소자승 해
    coeffs = pinv(A) * received_preamble(1:N);
    h0 = coeffs(1);
    h1 = coeffs(2);
    bias = coeffs(3);

    % 계수 정규화
    total_energy = sqrt(h0^2 + h1^2);
    if total_energy > 0
        h0 = h0 / total_energy;
        h1 = h1 / total_energy;
    end
end

%------------------------------------------------------------------------
% Viterbi MLSD 알고리즘
function [detected_bits, path_metrics] = viterbi_mlsd(received_signal, h0, h1, bias, window_size, traceback_depth)
    % Viterbi Maximum Likelihood Sequence Detection
    % received_signal: 수신 신호
    % h0, h1: 채널 임펄스 응답 계수
    % bias: DC 바이어스
    % window_size: 처리할 윈도우 크기
    % traceback_depth: 역추적 깊이

    N = min(window_size, length(received_signal));
    if N < traceback_depth
        detected_bits = [];
        path_metrics = [];
        return;
    end

    % 상태 정의: [이전 비트, 현재 비트] = [00, 01, 10, 11]
    num_states = 4;
    INF = 1e10;

    % 경로 메트릭 초기화
    path_metrics = INF * ones(num_states, N+1);
    path_metrics(2, 1) = 0;  % 시작 상태 01 (start bit 끝)

    % 생존 경로 저장
    survivor_paths = zeros(num_states, N);

    % 예상 신호값 계산 (NRZI 및 ISI 고려)
    % NRZI: 0 -> 이전 상태 유지, 1 -> 상태 반전
    % 상태 [prev_nrzi, curr_nrzi]에서의 예상 신호값
    expected_signals = zeros(num_states, 2);  % [input_bit=0일때, input_bit=1일때]

    % 상태 1 (00): prev_nrzi=0, curr_nrzi=0
    expected_signals(1, 1) = bias + h0*(-1) + h1*(-1);  % input=0 -> nrzi=0 (-1)
    expected_signals(1, 2) = bias + h0*(+1) + h1*(-1);  % input=1 -> nrzi=1 (+1)

    % 상태 2 (01): prev_nrzi=0, curr_nrzi=1
    expected_signals(2, 1) = bias + h0*(+1) + h1*(-1);  % input=0 -> nrzi=1 (+1)
    expected_signals(2, 2) = bias + h0*(-1) + h1*(-1);  % input=1 -> nrzi=0 (-1)

    % 상태 3 (10): prev_nrzi=1, curr_nrzi=0
    expected_signals(3, 1) = bias + h0*(-1) + h1*(+1);  % input=0 -> nrzi=0 (-1)
    expected_signals(3, 2) = bias + h0*(+1) + h1*(+1);  % input=1 -> nrzi=1 (+1)

    % 상태 4 (11): prev_nrzi=1, curr_nrzi=1
    expected_signals(4, 1) = bias + h0*(+1) + h1*(+1);  % input=0 -> nrzi=1 (+1)
    expected_signals(4, 2) = bias + h0*(-1) + h1*(+1);  % input=1 -> nrzi=0 (-1)

    % Forward pass - Viterbi 알고리즘
    for t = 1:N
        new_path_metrics = INF * ones(num_states, 1);

        for curr_state = 1:num_states
            for input_bit = 0:1
                % 상태 전이 계산
                next_state = get_next_state(curr_state, input_bit);

                % 브랜치 메트릭 계산 (유클리드 거리)
                expected_signal = expected_signals(curr_state, input_bit+1);
                branch_metric = (received_signal(t) - expected_signal)^2;

                % 경로 메트릭 업데이트
                candidate_metric = path_metrics(curr_state, t) + branch_metric;

                if candidate_metric < new_path_metrics(next_state)
                    new_path_metrics(next_state) = candidate_metric;
                    survivor_paths(next_state, t) = curr_state;
                end
            end
        end

        path_metrics(:, t+1) = new_path_metrics;
    end

    % Traceback - 최적 경로 찾기
    [~, best_final_state] = min(path_metrics(:, N+1));
    detected_bits = zeros(1, N);

    current_state = best_final_state;
    for t = N:-1:max(1, N-traceback_depth+1)
        if t > 1
            prev_state = survivor_paths(current_state, t);
            detected_bits(t) = get_transmitted_bit(prev_state, current_state);
            current_state = prev_state;
        end
    end

    % 출력은 이미 0, 1 형태이므로 추가 변환 불필요
end

%------------------------------------------------------------------------
% NRZI 상태 전이 함수
function next_state = get_next_state(current_state, input_bit)
    % 상태: 1=00, 2=01, 3=10, 4=11 (prev_nrzi, curr_nrzi)
    % input_bit: 0 또는 1
    % NRZI: input=0이면 이전 상태 유지, input=1이면 반전

    switch current_state
        case 1  % 00 (prev=0, curr=0)
            if input_bit == 0
                next_state = 1;  % 0 유지 -> 00 -> 00
            else
                next_state = 2;  % 0 반전 -> 00 -> 01
            end
        case 2  % 01 (prev=0, curr=1)
            if input_bit == 0
                next_state = 4;  % 1 유지 -> 01 -> 11
            else
                next_state = 3;  % 1 반전 -> 01 -> 10
            end
        case 3  % 10 (prev=1, curr=0)
            if input_bit == 0
                next_state = 1;  % 0 유지 -> 10 -> 00
            else
                next_state = 2;  % 0 반전 -> 10 -> 01
            end
        case 4  % 11 (prev=1, curr=1)
            if input_bit == 0
                next_state = 4;  % 1 유지 -> 11 -> 11
            else
                next_state = 3;  % 1 반전 -> 11 -> 10
            end
        otherwise
            next_state = 1;
    end
end

%------------------------------------------------------------------------
% NRZI 전송된 비트 복원
function transmitted_bit = get_transmitted_bit(prev_state, curr_state)
    % NRZI 상태 전이로부터 전송된 비트 추정
    % 상태: 1=00, 2=01, 3=10, 4=11 (prev_nrzi, curr_nrzi)

    % NRZI 상태 전이 테이블
    % [prev_state][input_bit] -> next_state
    nrzi_transitions = [
        1, 2;  % 00 -> 00(input=0), 01(input=1)
        4, 3;  % 01 -> 11(input=0), 10(input=1)
        1, 2;  % 10 -> 00(input=0), 01(input=1)
        4, 3   % 11 -> 11(input=0), 10(input=1)
    ];

    if curr_state == nrzi_transitions(prev_state, 1)
        transmitted_bit = 0;  % 입력 비트 0
    elseif curr_state == nrzi_transitions(prev_state, 2)
        transmitted_bit = 1;  % 입력 비트 1
    else
        transmitted_bit = 0;  % 오류 시 기본값
    end
end

%------------------------------------------------------------------------
% CRC 계산 (AIS 표준)
function crc = update_crc(crc, new_bit)
    if bitand(bitxor(crc, new_bit), 0x0001)
        crc = bitxor(bitshift(crc, -1), 0x8408);
    else
        crc = bitshift(crc, -1);
    end
end


%-------------------------------------------------------------------------
% impulse response of gmsk filter
%-------------------------------------------------------------------------
%SPAN = 3; SPS = 4;
%impulse_response_of_gmsk            = gmsk_impulse_response(BT, OSR, SPAN*SPS, 1);
SPAN = 3; SPS = 4;
impulse_response_of_gmsk            = gaussdesign(BT, SPAN, SPS);
impulse_response_of_gmsk_twice      = conv (impulse_response_of_gmsk, impulse_response_of_gmsk);
RX_GMSK_BT_0_5_FIR_N                = length(impulse_response_of_gmsk);

preamble_zero_padded                = upsample (preamble, OSR);
preamble_filtered_by_gmsk           = conv (preamble_zero_padded, impulse_response_of_gmsk);
preamble_filtered_by_gmsk_twice     = conv (preamble_zero_padded, impulse_response_of_gmsk_twice);

if (ENABLE_PLOT1 == 1)
    h_fig10 = figure(10);
    h_fig10.Name = 'gmsk and preamble';
    subplot(3,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse\_response (gmsk) (o)');
    subplot(3,1,2); plot(preamble, '-o'); grid; title('preamble');
    subplot(3,1,3); plot(preamble_filtered_by_gmsk, '-o');
end

%-------------------------------------------------------------------------
% Variables
%-------------------------------------------------------------------------
G_vRxRawDataBuff        = zeros(1, RX_GMSK_BT_0_5_FIR_N);
G_xPreData              = struct('nPntX', uint8(0), ...
                                'dSumX', double(0), ...
                                'dCntX', uint16(0), ...
                                'wAvrX', double(DC_MID_LEVEL), ...
                                'vData', zeros(1,RX_PRE_MAX_BUF_SIZE));
G_xDotData              = struct('wDotPattern', uint16(0), ...
                                'wDotChanged', uint8(0), ...
                                'wDotCountX', uint8(0));
G_wRxShiftReg           = 0;
G_dSwRxPllCntrX         = 0;
G_dSwRxPllSampP         = 0;
G_dSwRxPllSampC         = 0;
G_wRxCurrBitD           = 0;
G_wRxPrevBitD           = 0;
G_wCrcRegData           = 0;
G_wRxBitCount           = 0;

G_wRxAfAdcData          = 0;
G_wRxNrziCntr           = 0;
G_wRxNrziCurr           = 0;
G_wRxNrziPrev           = 0;
G_wRxNrziTemp           = 0;
G_wRxReferValue         = DC_MID_LEVEL;
G_wRxRunStatus          = RX_MDM_STATUS_PREAMBLE;
G_dSwRxPllValue         = 0;
G_dRxAdcErrCnt          = 0;

G_wNewBitData           = 0;
G_bRxByteData           = 0;

G_PreStart              = 0;
G_PreOffset             = 300;
G_dSyncDetCnt           = 0;
G_dAdcErrCnt            = 0;
G_dStartErrCnt          = 0;
G_dPloadErrCnt          = 0;
G_dStuffErrCnt          = 0;
G_dCrcErrCnt            = 0;
G_dRcvPktCnt            = 0;

G_dRxAfAdcSumVal        = 0;
G_dRxAfAdcCntVal        = 0;
G_dMaxSyncCorrel        = 0;
G_dMaxSyncCnt           = 0;
G_dSyncSymbolIndex      = 0;
G_dStartSymbolIndex     = 0;

G_BitDataArray = zeros(1, 500);

% 개선된 성능을 위한 추가 변수들
G_CorrelHistory         = zeros(1, 100);    % 상관관계 히스토리
G_AdaptiveThreshold     = MAX_SYNC_CORRVAL; % 적응형 임계값
G_NoiseFloor            = 0.1;              % 노이즈 플로어
G_SignalQuality         = 0;                % 신호 품질 지표
G_HysteresisValue       = 0.02;             % NRZI 히스테리시스 값

% 개선된 Adaptive DC 관련 변수들
G_DcState = struct('updated', false, 'error', 0);
G_KalmanState = struct('gain', 0, 'variance', 0);
G_HpfState = struct('buffer_full', false, 'variance', 0);
G_DcMethod = ADAPTIVE_DC_METHOD;  % 설정에서 가져옴

% DC 성능 모니터링 변수들
G_DcUpdateCount = 0;
G_DcErrorHistory = zeros(1, 1000);
G_DcMethodPerformance = zeros(4, 3);  % [method, update_count, avg_error]

% 기존 방법 최적화를 위한 추가 변수들
G_DcStabilityCounter = 0;      % DC 안정성 카운터
G_DcVarianceWindow = zeros(1, DC_VARIANCE_WINDOW);  % DC 변화량 윈도우
G_DcAdaptiveAlpha = DC_BASE_ALPHA;     % 적응형 알파값

% Viterbi MLSD 관련 변수들
G_ViterbiEnabled = ENABLE_VITERBI_MLSD;     % Viterbi MLSD 활성화 플래그
G_ChannelH0 = 1.0;                          % 채널 임펄스 응답 h0
G_ChannelH1 = 0.0;                          % 채널 임펄스 응답 h1
G_ChannelBias = 0.0;                        % 채널 DC 바이어스
G_ViterbiBuffer = zeros(1, VITERBI_WINDOW_SIZE);  % Viterbi 처리 버퍼
G_ViterbiBufferIndex = 1;                   % 버퍼 인덱스
G_ViterbiDetectionCount = 0;                % Viterbi 검출 카운터
G_ChannelEstimationCount = 0;               % 채널 추정 카운터
G_ViterbiConfidenceHistory = zeros(1, 100); % Viterbi 신뢰도 히스토리
G_LastPreambleSignal = [];                  % 마지막 프리앰블 신호
G_ViterbiStats = struct('success_count', 0, 'total_count', 0, 'avg_confidence', 0);  % Viterbi 통계

%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
if (USE_CHx_RAW_DATA == 0)
    G_hDumpFile = fopen('./DumpData/DUMPDATA_250525_ch1.bin');
else
    G_hDumpFile = fopen('./DumpData/DUMPDATA_250525_ch2.bin');
end
G_pSrcDataCh1 = fread(G_hDumpFile, 'uint16');

G_pSrcDataCh1 = (G_pSrcDataCh1-ADC_SUB_DC_OFFSET) / ADC_MAX_VALUE;

% 성능 향상을 위한 배열 사전 할당
data_length = length(G_pSrcDataCh1);
G_pFilteredData = zeros(1, data_length);
CorrelPreamble = zeros(1, data_length);
AdativeDcOffset = zeros(1, data_length);
BitArray = zeros(1, data_length);
G_dCRCErrSymIdx = zeros(1, 1000);  % CRC 오류 인덱스 배열 사전 할당

for nCurSymbolIdx = 1:length(G_pSrcDataCh1)
    % Notch Filter
    if (ENABLE_NOTCH_FLT == 1)
        G_wRxAfAdcData = apply_notch_filter(G_pSrcDataCh1(nCurSymbolIdx), G_vNotchDataX, NOTCH_FLT_A, NOTCH_FLT_B);
    else
        G_wRxAfAdcData = G_pSrcDataCh1(nCurSymbolIdx);
    end

    G_vRxRawDataBuff(1:1) = [];
    G_vRxRawDataBuff(RX_GMSK_BT_0_5_FIR_N) = G_wRxAfAdcData;

    % GMSK Filter
    G_pFilteredData(nCurSymbolIdx) = apply_gmsk_filter(G_vRxRawDataBuff, impulse_response_of_gmsk);

    G_vGmskPreamble = preamble_filtered_by_gmsk;
    % Correlation Preamble
    if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        if (nCurSymbolIdx <= length(G_vGmskPreamble))
            CorrelPreamble(nCurSymbolIdx) = 0;
        else
            % Preamble correlation detection은 GMSK Filter를 통과한 데이터를 사용하지 않는다.
            %if (ENABLE_GMSK_RX_FLT > 0)
            %    tmp_100 = G_pFilteredData(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx);
            %    tmp_100 = tmp_100/norm(tmp_100);
            %    CorrelPreamble(nCurSymbolIdx) = (tmp_100) * G_vGmskPreamble / norm(G_vGmskPreamble);
            %else
                tmp_100 = G_pSrcDataCh1(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx);
                tmp_100 = tmp_100/norm(tmp_100);
                CorrelPreamble(nCurSymbolIdx) = (tmp_100)' * G_vGmskPreamble / norm(G_vGmskPreamble);
            %end

            % 개선된 프리앰블 검출 (적응형 임계값 사용)
            % 상관관계 히스토리 업데이트
            G_CorrelHistory = [G_CorrelHistory(2:end), CorrelPreamble(nCurSymbolIdx)];

            % 신호 품질 평가
            recent_correl = G_CorrelHistory(max(1, end-10):end);
            G_SignalQuality = mean(recent_correl) / (std(recent_correl) + 0.01);

            if (CorrelPreamble(nCurSymbolIdx) > MAX_SYNC_CORRVAL && CorrelPreamble(nCurSymbolIdx) > G_dMaxSyncCorrel)
                G_dMaxSyncCorrel = CorrelPreamble(nCurSymbolIdx);
                G_dSyncSymbolIndex = nCurSymbolIdx;
                G_dMaxSyncCnt = 0;
            elseif (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL)
                G_dMaxSyncCnt = G_dMaxSyncCnt + 1;
            end
                
            if (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL && G_dMaxSyncCnt >= MAX_SYNC_COUNT)
                range = (G_dSyncSymbolIndex-DC_AVG_COUNT-DC_AVG_OFFSET+1:G_dSyncSymbolIndex-DC_AVG_OFFSET);
                if (ENABLE_GMSK_RX_FLT > 0)
                    dc_sum = sum(G_pFilteredData(range));
                else
                    dc_sum = sum(G_pSrcDataCh1(range));
                end
                G_wRxReferValue = dc_sum / length(range) + DC_GAP;

                if (nCurSymbolIdx > G_PreOffset)
                    G_PreStart = nCurSymbolIdx-G_PreOffset;
                else
                    G_PreStart = 1;
                end

                %--------------------------------------------
                if (ENABLE_PLOT1 == 1)
                    h_fig1 = figure(1);
                    h_fig1.Name = 'Detected Preamble Data(Matched Filter)';
                    x1 = G_PreStart:nCurSymbolIdx;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, CorrelPreamble(x1), '-+'); grid; 
                    title('Detected\_Preamble_Data'); yline(G_wRxReferValue,'-m',G_wRxReferValue,'LineWidth',2);
                    xline(G_dSyncSymbolIndex,'--', 'Max picked correlation');
                    xline(G_dSyncSymbolIndex-DC_AVG_COUNT-DC_AVG_OFFSET+1,'--', 'DC Offset sum start');
                    xline(G_dSyncSymbolIndex-DC_AVG_OFFSET,'--', 'DC Offset sum end');
                end
                %--------------------------------------------

                % Viterbi MLSD를 위한 채널 추정
                if (G_ViterbiEnabled == 1 && ENABLE_CHANNEL_ESTIMATION == 1)
                    % 프리앰블 신호 추출
                    preamble_start_idx = G_dSyncSymbolIndex - length(G_vGmskPreamble) + 1;
                    preamble_end_idx = G_dSyncSymbolIndex;

                    if preamble_start_idx > 0 && preamble_end_idx <= length(G_pFilteredData)
                        if (ENABLE_GMSK_RX_FLT > 0)
                            received_preamble = G_pFilteredData(preamble_start_idx:preamble_end_idx);
                        else
                            received_preamble = G_pSrcDataCh1(preamble_start_idx:preamble_end_idx);
                        end

                        % 알려진 프리앰블 패턴 (NRZI 인코딩된 형태)
                        known_preamble_nrzi = zeros(size(G_vGmskPreamble));
                        prev_bit = 0;
                        for i = 1:length(preamble)
                            if preamble(i) == 0
                                known_preamble_nrzi(i) = prev_bit;  % 0이면 이전 상태 유지
                            else
                                known_preamble_nrzi(i) = 1 - prev_bit;  % 1이면 반전
                            end
                            prev_bit = known_preamble_nrzi(i);
                        end

                        % 채널 추정 수행
                        [new_h0, new_h1, new_bias] = estimate_channel_response(received_preamble', known_preamble_nrzi, OSR);

                        % 채널 계수 유효성 검사
                        if abs(new_h0) > 0.01 && abs(new_h1) > 0.01 && abs(new_h0) + abs(new_h1) > 0.5
                            % 채널 계수 업데이트 (지수 평활화)
                            if G_ChannelEstimationCount == 0
                                G_ChannelH0 = new_h0;
                                G_ChannelH1 = new_h1;
                                G_ChannelBias = new_bias;
                            else
                                % 신호 품질에 따른 적응형 업데이트 비율
                                if G_SignalQuality > 3.0
                                    alpha = CHANNEL_UPDATE_RATE * 1.5;  % 신호가 좋으면 더 빠른 적응
                                elseif G_SignalQuality > 2.0
                                    alpha = CHANNEL_UPDATE_RATE;  % 기본 적응 속도
                                else
                                    alpha = CHANNEL_UPDATE_RATE * 0.5;  % 신호가 나쁘면 느린 적응
                                end

                                G_ChannelH0 = (1-alpha) * G_ChannelH0 + alpha * new_h0;
                                G_ChannelH1 = (1-alpha) * G_ChannelH1 + alpha * new_h1;
                                G_ChannelBias = (1-alpha) * G_ChannelBias + alpha * new_bias;
                            end
                        end

                        G_ChannelEstimationCount = G_ChannelEstimationCount + 1;
                        G_LastPreambleSignal = received_preamble;

                        % 디버그 정보
                        if (ENABLE_DEBUG == 1)
                            fprintf('Channel Estimation #%d: h0=%.4f, h1=%.4f, bias=%.4f\n', ...
                                G_ChannelEstimationCount, G_ChannelH0, G_ChannelH1, G_ChannelBias);
                        end
                    end
                end

                %%
                G_dStartSymbolIndex = nCurSymbolIdx - START_DETECT_OFFSET;
                G_dMaxSyncCorrel= 0;
                G_dMaxSyncCnt   = 0;

                G_wRxRunStatus  = RX_MDM_STATUS_START;
                G_wRxShiftReg   = 0;
                G_wRxBitCount   = 0;
                G_wRxPrevBitD   = G_wRxNrziPrev;
                G_wBitSamplCntr = 1;
                G_dRxAdcErrCnt  = 0;

                G_dSwRxPllValue = RX_PLL_HALF;
                G_dSwRxPllCntrX = 1;
                G_dSwRxPllSampC = G_wRxNrziPrev;
                G_dSwRxPllSampP = G_wRxNrziPrev;

                G_xPreData.nPntX = 0;
                G_xPreData.dSumX = 0;
                G_xPreData.dCntX = 0;
                G_xPreData.wAvrX = DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;

                G_dRxAfAdcSumVal = 0;
                G_dRxAfAdcCntVal = 0;

                G_dSyncDetCnt = G_dSyncDetCnt + 1;
            end
        end
    end

    AdativeDcOffset(nCurSymbolIdx) = G_wRxReferValue;

    % Check Max/Min Lever voltage range
    if (    (ENABLE_ADC_LIMIT == 1) && (G_pSrcDataCh1(nCurSymbolIdx) < DC_MIN_LEVEL || G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL)) ...
        || ((ENABLE_ADC_LIMIT == 2) && (G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL))
    %if (    (ENABLE_ADC_LIMIT == 1) && (G_pFilteredData(nCurSymbolIdx) < DC_MIN_LEVEL || G_pFilteredData(nCurSymbolIdx) > DC_MAX_LEVEL)) ...
    %    || ((ENABLE_ADC_LIMIT == 2) && (G_pFilteredData(nCurSymbolIdx) > DC_MAX_LEVEL))
        G_xPreData.nPntX = 0;
        G_xPreData.dSumX = 0;
        G_xPreData.dCntX = 0;
        G_xPreData.wAvrX = DC_MID_LEVEL;

        if(G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        %if(G_wRxRunStatus <= RX_MDM_STATUS_START)
            G_dRxAdcErrCnt = 0;
            % Init preamble correlation value and count
            % AD Error의 경우 correlation max value and count를 초기화 해주면 수신 성능이
            % 좋아짐.
            G_dMaxSyncCorrel= 0;
            G_dMaxSyncCnt   = 0;
        else
            G_dRxAdcErrCnt = G_dRxAdcErrCnt + 1;
            if(G_dRxAdcErrCnt > ADC_MAX_ERROR_CNT)
                G_dRxAdcErrCnt = 0;

                %--------------------------------------------
                if (ENABLE_PLOT96 == 1)
                    h_fig96 = figure(96);
                    h_fig96.Name = 'Adc Max/Min Error';
                    x1 = G_PreStart:nCurSymbolIdx-SYNC_DETECT_OFFSET+50;
                    x2 = G_PreStart:nCurSymbolIdx-SYNC_DETECT_OFFSET;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m'); grid; 
                    title('Error Max/Min Vol.');
                end
                %--------------------------------------------

                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                G_wRxShiftReg  = 0;
                G_wRxReferValue= DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;
                G_dAdcErrCnt = G_dAdcErrCnt+1;
            end
        end
    end

    if (G_wRxRunStatus ~= RX_MDM_STATUS_PREAMBLE)
        for idx = (G_dStartSymbolIndex : nCurSymbolIdx)
            if (ENABLE_GMSK_RX_FLT > 0)
                G_wRxAfAdcData = G_pFilteredData(idx);
            else
                G_wRxAfAdcData = G_pSrcDataCh1(idx);
            end

            % 하이브리드 방식: 기본은 DC offset, 신호 품질이 낮을 때 Viterbi 보조 사용
            dc_bit = nrzi_decode(G_wRxAfAdcData, G_wRxReferValue);
            G_wRxNrziCurr = dc_bit;  % 기본값

            if (G_ViterbiEnabled == 1 && G_ChannelEstimationCount > 5)
                % Viterbi 버퍼에 샘플 추가
                G_ViterbiBuffer(G_ViterbiBufferIndex) = G_wRxAfAdcData;
                G_ViterbiBufferIndex = G_ViterbiBufferIndex + 1;

                % 신호 품질이 낮거나 DC offset 신뢰도가 낮을 때만 Viterbi 사용
                dc_confidence = abs(G_wRxAfAdcData - G_wRxReferValue);
                use_viterbi = (G_SignalQuality < 2.0) || (dc_confidence < 0.02);

                % 버퍼가 충분히 찼고 Viterbi 사용 조건을 만족할 때
                if G_ViterbiBufferIndex > VITERBI_WINDOW_SIZE && use_viterbi
                    % 간단한 Viterbi MLSD 수행 (작은 윈도우)
                    small_window = min(16, VITERBI_WINDOW_SIZE);
                    small_traceback = min(8, VITERBI_TRACEBACK_DEPTH);

                    [detected_bits, path_metrics] = viterbi_mlsd(G_ViterbiBuffer(end-small_window+1:end), G_ChannelH0, G_ChannelH1, G_ChannelBias, small_window, small_traceback);

                    if ~isempty(detected_bits) && length(detected_bits) >= small_traceback
                        % 가장 최근 비트 사용
                        viterbi_bit = detected_bits(end);

                        % DC offset과 Viterbi 결과 비교
                        if dc_bit == viterbi_bit
                            % 일치하면 신뢰도 높음
                            G_wRxNrziCurr = dc_bit;
                            G_ViterbiStats.success_count = G_ViterbiStats.success_count + 1;
                        else
                            % 불일치하면 신호 품질에 따라 선택
                            if G_SignalQuality < 1.5
                                G_wRxNrziCurr = viterbi_bit;  % 신호가 매우 나쁘면 Viterbi 선택
                                G_ViterbiDetectionCount = G_ViterbiDetectionCount + 1;
                            else
                                G_wRxNrziCurr = dc_bit;  % 그 외에는 DC offset 선택
                            end
                        end

                        % 신뢰도 히스토리 업데이트
                        if ~isempty(path_metrics)
                            min_metric = min(path_metrics(:, end));
                            max_metric = max(path_metrics(:, end));
                            if max_metric > min_metric
                                viterbi_confidence = 1 - (min_metric / max_metric);
                                G_ViterbiConfidenceHistory = [G_ViterbiConfidenceHistory(2:end), viterbi_confidence];
                            end
                        end
                    end

                    G_ViterbiStats.total_count = G_ViterbiStats.total_count + 1;

                    % 버퍼 슬라이딩
                    slide_size = VITERBI_WINDOW_SIZE / 4;  % 더 작은 슬라이딩
                    G_ViterbiBuffer(1:slide_size) = G_ViterbiBuffer(end-slide_size+1:end);
                    G_ViterbiBufferIndex = slide_size + 1;
                end
            end

            G_dSwRxPllSampC = G_wRxNrziCurr;

            %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
            % 아래 코드 사용시 수신율이 높아짐.
            %
            if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                if((G_wRxNrziCntr <= (OSR - 2) || (G_wRxNrziCntr == (OSR - 1) && G_dSwRxPllValue >= (RX_PLL_FULL - RX_PLL_INCR + RX_PLL_STEP))))
                %if(G_wRxNrziCntr <= (OSR - 2))
                    G_wRxNrziCntr = G_wRxNrziCntr + 1;
                    G_dSwRxPllSampC = G_dSwRxPllSampP;
                else
                    G_wRxNrziCntr = 1;
                end
            else
                G_wRxNrziCntr = G_wRxNrziCntr + 1;
            end
            %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

            if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                if (G_wRxRunStatus == RX_MDM_STATUS_START)
                    if (G_dSwRxPllCntrX >= (OSR * 2 - 3) && G_dSwRxPllCntrX <= (OSR * 2 + 3))
                        G_dSwRxPllValue = RX_PLL_HALF;
                    end
                end
        
                if (G_dSwRxPllValue < RX_PLL_HALF)
                    G_dSwRxPllValue = (G_dSwRxPllValue + RX_PLL_STEP);
                else
                    G_dSwRxPllValue = (G_dSwRxPllValue - RX_PLL_STEP);
                end

                G_dSwRxPllCntrX = 1;
            else
                G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
            end

            G_dSwRxPllSampP = G_dSwRxPllSampC;

            G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;
            if(G_dSwRxPllValue >= RX_PLL_FULL)
                G_dSwRxPllValue = G_dSwRxPllValue - RX_PLL_FULL;
            else
                continue;
            end

            G_wRxCurrBitD = G_dSwRxPllSampC;

            %%%%% ProcessRxDataCommonRun()
            G_wRxShiftReg = bitshift(G_wRxShiftReg, 1);
            if (G_wRxCurrBitD == G_wRxPrevBitD)
                G_wRxShiftReg = bitor(G_wRxShiftReg, 0x0001);
                BitArray(idx) = 0.05;
            else
                G_wRxShiftReg = bitand(G_wRxShiftReg, 0xfffe);
                BitArray(idx) = 0.015;

                if (ENABLE_ADAPT_DC_OFFSET == 1)
                    % 개선된 적응형 DC Offset 추적 - 다중 방법 지원
                    % 신호 품질에 따른 적응형 알파 값
                    if G_SignalQuality > 2.5
                        alpha_dc = 0.900;  % 신호가 좋으면 더 빠른 적응
                    elseif G_SignalQuality > 1.5
                        alpha_dc = 0.850;  % 기본값
                    else
                        alpha_dc = 0.800;  % 신호가 나쁠 때는 더 느린 적응
                    end

                    % 반전 구간에서만 DC Offset 보정
                    if (ENABLE_GMSK_RX_FLT > 0)
                        sample = G_pFilteredData(idx);
                    else
                        sample = G_pSrcDataCh1(idx);
                    end

                    % 선택된 방법에 따라 DC 업데이트
                    old_dc_value = G_wRxReferValue;
                    switch G_DcMethod
                        case 1  % Enhanced (다중 시간 상수 + 히스테리시스)
                            [G_wRxReferValue, G_DcState] = update_dc_offset_enhanced(G_wRxReferValue, sample, alpha_dc, G_SignalQuality, G_DcState);
                            if G_DcState.updated
                                G_DcUpdateCount = G_DcUpdateCount + 1;
                                G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = G_DcState.error;
                            end
                        case 2  % Kalman 필터
                            [G_wRxReferValue, G_KalmanState] = update_dc_offset_kalman(G_wRxReferValue, sample, G_KalmanState);
                            G_DcUpdateCount = G_DcUpdateCount + 1;
                            dc_error = abs(sample - old_dc_value);
                            G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = dc_error;
                        case 3  % 주파수 도메인 (이동평균)
                            [G_wRxReferValue, G_HpfState] = update_dc_offset_hpf(sample, G_HpfState);
                            G_DcUpdateCount = G_DcUpdateCount + 1;
                            dc_error = abs(sample - old_dc_value);
                            G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = dc_error;
                        case 4  % 기존 방법 (최적화된)
                            % DC 변화량 추적
                            dc_change = abs(sample - G_wRxReferValue);
                            G_DcVarianceWindow = [G_DcVarianceWindow(2:end), dc_change];
                            dc_variance = var(G_DcVarianceWindow);

                            % 신호 품질과 DC 안정성을 고려한 적응형 알파 조정
                            base_alpha = DC_BASE_ALPHA;  % 설정에서 가져온 기본 알파값

                            % 신호 품질 기반 조정
                            if G_SignalQuality > 3.0
                                quality_factor = 1.08;  % 매우 좋은 신호: 더 빠른 적응
                            elseif G_SignalQuality > 2.5
                                quality_factor = 1.06;  % 좋은 신호
                            elseif G_SignalQuality > 2.0
                                quality_factor = 1.02;  % 보통 신호
                            elseif G_SignalQuality > 1.5
                                quality_factor = 1.00;  % 기본값
                            elseif G_SignalQuality > 1.0
                                quality_factor = 0.97;  % 약한 신호
                            else
                                quality_factor = 0.94;  % 매우 약한 신호: 더 느린 적응
                            end

                            % DC 안정성 기반 조정
                            if dc_variance < DC_STABILITY_THRESHOLD
                                stability_factor = 0.98;  % 매우 안정: 느린 적응
                                G_DcStabilityCounter = G_DcStabilityCounter + 1;
                            elseif dc_variance < (DC_STABILITY_THRESHOLD * 5)
                                stability_factor = 0.99;  % 안정: 약간 느린 적응
                                G_DcStabilityCounter = max(0, G_DcStabilityCounter - 1);
                            else
                                stability_factor = 1.02;  % 불안정: 빠른 적응
                                G_DcStabilityCounter = 0;
                            end

                            % 최종 알파값 계산
                            alpha_dc = base_alpha * quality_factor * stability_factor;
                            alpha_dc = max(DC_ALPHA_MIN, min(DC_ALPHA_MAX, alpha_dc));  % 설정된 범위로 제한

                            % 적응형 알파값 업데이트 (부드러운 변화)
                            G_DcAdaptiveAlpha = 0.9 * G_DcAdaptiveAlpha + 0.1 * alpha_dc;

                            % 기존 방법 적용 (최적화된 알파값 사용)
                            G_wRxReferValue = G_DcAdaptiveAlpha * G_wRxReferValue + (1-G_DcAdaptiveAlpha) * sample;
                            G_DcUpdateCount = G_DcUpdateCount + 1;
                            dc_error = abs(sample - old_dc_value);
                            G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = dc_error;
                        otherwise
                            % 기본값: Enhanced 방법
                            [G_wRxReferValue, G_DcState] = update_dc_offset_enhanced(G_wRxReferValue, sample, alpha_dc, G_SignalQuality, G_DcState);
                            if G_DcState.updated
                                G_DcUpdateCount = G_DcUpdateCount + 1;
                                G_DcErrorHistory(mod(G_DcUpdateCount-1, 1000)+1) = G_DcState.error;
                            end
                    end
                end
            end

            G_wRxPrevBitD = G_wRxCurrBitD;

            switch (G_wRxRunStatus)
                case RX_MDM_STATUS_START
                    % 개선된 시작 비트 검출 (더 유연한 패턴 매칭)
                    start_pattern_detected = false;

                    % 다양한 시작 패턴 검사 (노이즈에 더 강건)
                    if (bitand(G_wRxShiftReg, 0x003f) == 0x003e || ...
                        bitand(G_wRxShiftReg, 0x001f) == 0x001e || ...
                        bitand(G_wRxShiftReg, 0x007f) == 0x007e)
                        start_pattern_detected = true;
                    end

                    if start_pattern_detected
                        %%----------------------------------------------------------
                        if (ENABLE_PLOT2 == 1)
                            h_fig2 = figure(2);
                            h_fig2.Name = 'Detected Start Data';
                            x1 = G_PreStart:idx;
                            x2 = G_PreStart:idx;
                            subplot(2,1,1); plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, AdativeDcOffset(x1), '-m'); grid; 
                                            title('detected\_start\_data');
                            subplot(2,1,2); plot(x2, G_pSrcDataCh1(x2), '-x', x1, G_pFilteredData(x1), '-o'); grid; 
                                            title('filtered\_start\_data');
                        end
                        %----------------------------------------------------------

                        G_wRxBitCount    = 0;
                        G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                    else
                        G_wRxBitCount = G_wRxBitCount + 1;
                        if(G_wRxBitCount >= 25)
                            %----------------------------------------------------------
                            if (ENABLE_PLOT97 == 1)
                                h_fig97 = figure(97);
                                h_fig97.Name = 'Start Bit Error';
                                x1 = G_PreStart:idx+100;
                                x2 = G_PreStart:idx;
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m', x2, BitArray(x2), '-+'); grid; 
                                xline(idx,'--', 'Current Positon');
                                title('Error Start Bit');
                            end
                            %----------------------------------------------------------

                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dStartErrCnt = G_dStartErrCnt + 1;
                        end
                    end

                case RX_MDM_STATUS_PRELOAD
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if (G_wRxBitCount == 8)
                        G_wRxBitCount = 0;
                        G_wCrcRegData = 0xffff;
                        G_wRxRunStatus= RX_MDM_STATUS_DATA;
                        %ClrRxRawFormTemp();

                        nTemp = bitshift(G_wRxShiftReg, 2);
                        nTemp = bitand(nTemp, 0x00ff);
                        nMsgID = G_vReverDataTableX(nTemp + 1);
                        if (nMsgID < 0 || nMsgID > 27)
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dPloadErrCnt = G_dPloadErrCnt + 1;
                        else
                            m_wRxMaxBitSize = (G_vMaxBitSize(nMsgID + 1) + 16 + 2);
                        end
                    end

                case RX_MDM_STATUS_DATA
                    if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)      % It's not a stuffing bit
                        G_wRxBitCount = G_wRxBitCount + 1;
                        if(G_wRxBitCount >= m_wRxMaxBitSize+7)
                            %----------------------------------------------------------
                            if (ENABLE_PLOT98 == 1)
                                h_fig98 = figure(98);
                                h_fig98.Name = 'Stuffing Bit Error';
                                x1 = G_PreStart:idx;
                                x2 = G_PreStart:idx;
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Error Stuffing Bit');
                            end
                            %----------------------------------------------------------

                            %%%%%% ResetToRxStatusPreamble()
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_bRxByteData = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dStuffErrCnt = G_dStuffErrCnt + 1;
                            continue;
                        end

                        G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                        G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));
                        G_BitDataArray(G_wRxBitCount) = G_wNewBitData;

                        G_wCrcRegData = update_crc(G_wCrcRegData, G_wNewBitData);
                    end

                    if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    %if (bitand(G_wRxShiftReg, 0x007f) == 0x007e)
                        if(G_wCrcRegData == 0xf0b8)                                 % This should give a result of 0xF0B8
                            %----------------------------------------------------------
                            if (ENABLE_PLOT3 == 1)
                                h_fig3 = figure(3);
                                h_fig3.Name = 'Received Data(CRC OK)';
                                x1 = G_PreStart:idx+50;
                                if (G_PreStart <= SYNC_DETECT_OFFSET)
                                    x2 = G_PreStart:nCurSymbolIdx;
                                    x3 = G_PreStart:nCurSymbolIdx;
                                else
                                    x2 = G_PreStart:idx;
                                    x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                                end
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Received AIS Packet');
                            end
                            %----------------------------------------------------------

                            if (G_PreStart > (215042-10) && G_PreStart < (215042+10))
                                break;
                            end

                            %WritePacketIntoRxRawBuff();
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_bRxByteData = 0;
                            G_dRcvPktCnt = G_dRcvPktCnt + 1;
                        else
                            %----------------------------------------------------------
                            if (ENABLE_PLOT99 == 1)
                                h_fig99 = figure(99);
                                h_fig99.Name = 'Received Data(CRC ERROR)';
                                x1 = G_PreStart:idx;
                                if (G_PreStart <= SYNC_DETECT_OFFSET)
                                    x2 = G_PreStart:nCurSymbolIdx;
                                    x3 = G_PreStart:nCurSymbolIdx;
                                else
                                    x2 = G_PreStart:idx;
                                    x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                                end
                                plot(x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Received Error AIS Packet');
                            end
                            %----------------------------------------------------------

                            %if (G_PreStart > (426187-10) && G_PreStart < (426187+10))
                            %    break;
                            %end

                            %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                            %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();

                            G_wRxBitCount    = 0;
                            %G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                            G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
                            G_bRxByteData    = 0;
                            G_dCrcErrCnt     = G_dCrcErrCnt + 1;
                            G_dCRCErrSymIdx(G_dCrcErrCnt) = G_PreStart;
                        end
                    end

                otherwise
                    warning('Unexpected run status.');
            end
        end
        G_dStartSymbolIndex = nCurSymbolIdx+1;
    end
end

%-------------------------------------------------------------------------
% DC 성능 분석 리포트
method_names = {'Enhanced', 'Kalman', 'HPF', 'Original'};
if G_DcUpdateCount > 0
    analyze_dc_performance(G_DcErrorHistory, G_DcUpdateCount, method_names{G_DcMethod});
end

% 수신 성능 통계
figure(9);
bar_x = ["SyncDet" "AdcErr" "StartErr" "StuffErr" "CrcErr" "Packet OK" ];
bar_y = [G_dSyncDetCnt, G_dAdcErrCnt G_dStartErrCnt G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt];
b = bar(bar_x, bar_y, 'FaceColor', 'flat');
b.CData(6,:) = [0.6350 0.0780 0.1840];
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')

% 성능 개선 정보 출력
fprintf('\n=== AIS Receiver Performance Summary ===\n');
fprintf('DC Method Used: %s\n', method_names{G_DcMethod});
fprintf('Total Packets Received: %d\n', G_dRcvPktCnt);
fprintf('Success Rate: %.2f%%\n', G_dRcvPktCnt / (G_dRcvPktCnt + G_dCrcErrCnt) * 100);
fprintf('DC Updates: %d\n', G_DcUpdateCount);

% Viterbi MLSD 성능 정보 출력
if G_ViterbiEnabled == 1
    fprintf('\n=== Viterbi MLSD Performance ===\n');
    fprintf('Viterbi MLSD Enabled: Yes\n');
    fprintf('Channel Estimations: %d\n', G_ChannelEstimationCount);
    fprintf('Channel Coefficients: h0=%.4f, h1=%.4f, bias=%.4f\n', G_ChannelH0, G_ChannelH1, G_ChannelBias);
    fprintf('Viterbi Detections: %d\n', G_ViterbiDetectionCount);

    if G_ViterbiStats.total_count > 0
        viterbi_success_rate = G_ViterbiStats.success_count / G_ViterbiStats.total_count * 100;
        fprintf('Viterbi Success Rate: %.2f%% (%d/%d)\n', viterbi_success_rate, G_ViterbiStats.success_count, G_ViterbiStats.total_count);
    end

    % Viterbi 신뢰도 통계
    if sum(G_ViterbiConfidenceHistory) > 0
        valid_confidences = G_ViterbiConfidenceHistory(G_ViterbiConfidenceHistory > 0);
        if ~isempty(valid_confidences)
            avg_viterbi_confidence = mean(valid_confidences);
            fprintf('Average Viterbi Confidence: %.3f\n', avg_viterbi_confidence);
            G_ViterbiStats.avg_confidence = avg_viterbi_confidence;
        end
    end

    fprintf('Window Size: %d symbols\n', VITERBI_WINDOW_SIZE);
    fprintf('Traceback Depth: %d symbols\n', VITERBI_TRACEBACK_DEPTH);
else
    fprintf('\n=== Viterbi MLSD Performance ===\n');
    fprintf('Viterbi MLSD Enabled: No (using conventional NRZI decoding)\n');
end

% 기존 방법 최적화 정보 (Method 4인 경우)
if G_DcMethod == 4
    fprintf('\n=== Original Method Optimization Info ===\n');
    fprintf('Final Adaptive Alpha: %.4f\n', G_DcAdaptiveAlpha);
    fprintf('DC Stability Counter: %d\n', G_DcStabilityCounter);
    fprintf('DC Variance: %.6f\n', var(G_DcVarianceWindow));
    fprintf('Average Signal Quality: %.3f\n', G_SignalQuality);
end
%-------------------------------------------------------------------------

function [h0, bias] = calc_coefficient(received_data, source_data, half_impulse_response)
    INDEX_START = 1;

    %received_data = received_data - 0.1;
    A = [source_data(INDEX_START:length(received_data))', ones(length(received_data),1)];

    pinv_data = pinv(A);
    received_data = received_data';
    coeff_vector = pinv_data*received_data;
    h0 = coeff_vector(1)*max(half_impulse_response);
    bias = coeff_vector(2);

    figure(34)
    kkk = 1:length(received_data);
    scale = h0/max(half_impulse_response);
    subplot(4,1,1); plot(received_data); grid; title('received\_data');
    subplot(4,1,2); plot(source_data(INDEX_START:INDEX_START+length(received_data)-1)); grid; title('source\_data');
    subplot(4,1,3); plot(kkk, scale*source_data(INDEX_START:INDEX_START+length(received_data)-1), '-o', kkk, scale*source_data(INDEX_START:INDEX_START+length(received_data)-1)+bias, '-x', kkk, received_data, '-+'); grid; title('source\_data (o), received\_data (x)');
    subplot(4,1,4); plot(kkk, pinv_data(1,:), '-o', kkk, pinv_data(2,:), '-x'); grid; title('test');
end